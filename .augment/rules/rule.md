---
type: "manual"
---

# AI助手开发快速规则 - 速查指南

## 🚨 核心规则 (必须遵守)

### 1. 文档优先 - 绝对权威
```
📚 项目文档 = 开发的唯一权威来源
🚫 禁止偏离文档进行开发
✅ 发现问题立即报告并等待澄清
```

### 2. 复用优先 - 避免重复
```
🔄 hive = 用户权限、前端架构
🔧 go-whatsapp-web-multidevice = WhatsApp API服务
🆕 新功能 = 基于文档设计开发
```

### 3. 渐进开发 - 按阶段执行
```
📋 阶段1: 基础架构 (第1-3周)
🔧 阶段2: 容器管理 (第4-6周)  
💬 阶段3: 消息同步 (第7-9周)
🖥️ 阶段4: 前端界面 (第10-13周)
🚀 阶段5: 高级功能 (第14-16周)
```

## 📚 开发前必读清单

### 每次开发任务前必须按顺序查阅：

1. **[接口设计总表](interface-overview.md)** ← 确认任务状态和优先级
2. **[开发计划](development-plan.md)** ← 确认当前阶段和任务
3. **[架构设计](architecture-design.md)** ← 理解系统架构和约束
4. **[API设计](api-design.md)** ← 查看接口定义和格式
5. **[数据库设计](database-design.md)** ← 查看数据模型和表结构

### 开发前检查清单：
- [ ] 任务在接口总表中有明确定义？
- [ ] 了解任务的复用状态（复用/扩展/新增）？
- [ ] API接口设计是否完整？
- [ ] 涉及的数据模型是否已定义？
- [ ] 是否需要复用hive功能？
- [ ] 是否需要集成go-whatsapp-web-multidevice？

## 🔧 开发执行要点

### 基于hive复用开发：
```bash
1. 查看hive中的类似功能
2. 分析现有代码结构和模式
3. 确保新代码与现有风格一致
4. 保持数据模型兼容性
```

### 新功能开发：
```bash
1. 严格按照API设计文档定义接口
2. 按照数据库设计文档创建数据模型
3. 遵循架构设计中的组件划分
4. 实现文档中定义的错误处理
```

### 容器集成开发：
```bash
1. 使用文档中的容器配置模板
2. 按照架构设计实现容器管理
3. 实现文档中定义的webhook处理
4. 确保代理池分配按设计实现
```

## ✅ 质量检查要点

### 代码质量：
- [ ] 符合文档中的设计规范
- [ ] API接口与文档定义完全一致
- [ ] 数据模型符合数据库设计
- [ ] 错误处理按照API设计实现
- [ ] 多租户隔离正确实现

### 功能验收：
- [ ] 功能完全按照文档要求实现
- [ ] 权限控制按照设计工作
- [ ] 性能满足架构设计要求
- [ ] 安全措施按照设计实现

## 🚨 问题处理流程

### 遇到文档不明确：
```
1. 🛑 立即停止开发
2. 📚 重新仔细阅读相关文档
3. 🔍 查看参考项目实现
4. ❓ 提出具体问题
5. ⏳ 等待澄清，不得自行决定
```

### 遇到文档冲突：
```
1. 📝 记录冲突点
2. 📊 分析优先级：
   - 接口设计总表 > 其他文档（进度状态）
   - API设计 > 其他文档（接口定义）
   - 数据库设计 > 其他文档（数据结构）
   - 架构设计 > 其他文档（整体设计）
3. ❓ 提出澄清请求
4. ⏸️ 暂停相关开发
```

### 发现文档错误：
```
1. 🚨 立即报告
2. 💡 提供修正建议
3. ⏸️ 暂停相关开发
4. ✅ 验证修正后继续
```

## 📊 当前项目状态

### 接口开发进度：
- ✅ **已完成**：19个接口 (28%) - 主要是复用hive
- 🚧 **开发中**：10个接口 (15%) - 租户管理、WhatsApp管理
- 📋 **待开发**：39个接口 (57%) - 消息、聊天、群发等

### 当前阶段重点：
```
🎯 阶段1: 基础架构搭建 (第1-3周)
📍 当前任务: 租户管理系统开发
🔄 复用策略: 基于hive扩展
⚠️ 关键风险: 多租户数据隔离
```

## 🎯 成功标准

### 开发质量：
- **100%符合文档** - 所有实现严格按文档进行
- **零偏离原则** - 不得有未经授权的偏离
- **完整性保证** - 实现文档中定义的所有功能
- **一致性维护** - 保持与现有代码的一致性

### 文档一致性：
- **代码即文档** - 代码与文档描述完全一致
- **及时同步** - 文档变更及时反映到代码
- **双向验证** - 代码验证文档，文档指导代码

## ⚠️ 严重违规行为

🚫 **绝对禁止的行为：**
1. 忽略文档进行开发
2. 私自修改核心设计
3. 偏离复用策略
4. 不按阶段进行开发

🔄 **违规后果：**
1. 立即停止开发
2. 回滚所有变更
3. 重新学习规则
4. 重新开始开发

## 📞 快速联系

### 查阅文档顺序：
1. **技术问题** → [架构设计](architecture-design.md)
2. **接口问题** → [API设计](api-design.md)
3. **数据问题** → [数据库设计](database-design.md)
4. **进度问题** → [开发计划](development-plan.md)
5. **状态问题** → [接口设计总表](interface-overview.md)

### 紧急情况：
- **阻塞性问题** → 立即报告并暂停开发
- **文档冲突** → 立即报告并等待澄清
- **技术风险** → 立即评估并报告

---

## 🔥 记住这句话

> **"文档是法律，复用是策略，质量是生命！"**

**在Hivers项目中，AI助手必须严格遵循文档进行开发，这是不可违反的铁律！**

---

**快速规则版本**：v1.0  
**对应完整规则**：[开发规则](development-rules.md)  
**适用范围**：所有AI助手开发工作
