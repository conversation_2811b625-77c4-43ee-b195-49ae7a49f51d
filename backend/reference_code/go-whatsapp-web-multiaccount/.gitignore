# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
bin/
dist/

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool
*.out

# Go workspace file
go.work

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Application specific
src/storages/
src/qrcode/
src/send_items/
src/media/
*.db
*.db-shm
*.db-wal
*.log
history-*.json

# Instance data
instance_*/

# Temporary files
*.tmp
*.temp
tmp/

# Environment files
.env
.env.local
.env.*.local

# Backup files
*.bak
*.backup

# Archive files
*.tar.gz
*.zip
*.rar

# Legacy files
main
main.exe
*.jpe
src/pkged.go