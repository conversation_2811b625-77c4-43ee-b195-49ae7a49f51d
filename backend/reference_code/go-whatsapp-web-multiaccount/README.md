# GO WhatsApp Web Multi Account

基于 [go-whatsapp-web-multidevice](https://github.com/aldinokemal/go-whatsapp-web-multidevice) 改造的多账户版本，支持同时管理多个 WhatsApp 账户，每个账户可以配置独立的网络代理。

## 🚀 新增功能

### 多账户管理
- ✅ 同时登录多个 WhatsApp 账户
- ✅ 每个账户独立的数据存储
- ✅ 动态创建和删除账户实例
- ✅ 实时监控所有账户状态

### 网络代理隔离
- ✅ 每个账户配置独立的 SOCKS5/HTTP 代理
- ✅ 代理故障自动检测和切换
- ✅ 代理连接测试功能
- ✅ 支持代理认证

### 统一管理界面
- ✅ RESTful API 接口
- ✅ 实例状态监控
- ✅ 系统资源监控
- ✅ 配置热更新

## 📋 API 端点

### 实例管理
```
POST   /api/instances                    # 创建实例
GET    /api/instances                    # 获取实例列表
GET    /api/instances/{id}               # 获取实例详情
PUT    /api/instances/{id}               # 更新实例配置
DELETE /api/instances/{id}               # 删除实例
```

### 实例操作
```
POST   /api/instances/{id}/login         # 实例登录
POST   /api/instances/{id}/login-code    # 配对码登录
POST   /api/instances/{id}/logout        # 实例登出
GET    /api/instances/{id}/status        # 实例状态
POST   /api/instances/{id}/reconnect     # 重新连接
```

### 代理管理
```
POST   /api/proxy/test                   # 测试代理连接
PUT    /api/instances/{id}/proxy         # 更新实例代理
```

### 系统管理
```
GET    /api/system/status               # 系统状态
```

## 🔧 使用示例

### 1. 创建实例
```bash
curl -X POST http://localhost:3000/api/instances \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Account-1",
    "config": {
      "auto_reply": "感谢您的消息",
      "auto_mark_read": false,
      "debug": true
    },
    "proxy_config": {
      "type": "socks5",
      "host": "proxy1.com",
      "port": 1080,
      "username": "user1",
      "password": "pass1",
      "enabled": true
    }
  }'
```

### 2. 获取实例列表
```bash
curl http://localhost:3000/api/instances
```

### 3. 实例登录
```bash
curl -X POST http://localhost:3000/api/instances/{instance_id}/login
```

### 4. 发送消息（使用实例）
```bash
curl -X POST http://localhost:3000/api/instances/{instance_id}/send/message \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "**********",
    "message": "Hello from instance 1"
  }'
```

### 5. 测试代理
```bash
curl -X POST http://localhost:3000/api/proxy/test \
  -H "Content-Type: application/json" \
  -d '{
    "type": "socks5",
    "host": "proxy.example.com",
    "port": 1080,
    "username": "user",
    "password": "pass"
  }'
```

## 🏗️ 架构设计

```
┌─────────────────┐
│   REST API      │ ← 统一入口
└─────────────────┘
         │
┌─────────────────┐
│ Instance        │ ← 实例管理器
│ Manager         │
└─────────────────┘
         │
    ┌────┴────┐
    │         │
┌───▼───┐ ┌───▼───┐
│ Inst1 │ │ Inst2 │ ← WhatsApp 实例
│Proxy1 │ │Proxy2 │ ← 独立代理
└───────┘ └───────┘
    │         │
    └────┬────┘
         │
┌─────────────────┐
│  数据存储        │ ← 独立数据库
│  instance_1/    │
│  instance_2/    │
└─────────────────┘
```

## 🚀 快速开始

### 1. 编译运行
```bash
cd src
go mod tidy
go build -o ../bin/multiaccount main.go
../bin/multiaccount rest
```

### 2. 访问管理界面
```
http://localhost:3000
```

### 3. 创建第一个实例
使用 API 或管理界面创建实例，配置代理，然后扫码登录。

## 📊 系统监控

### 获取系统状态
```bash
curl http://localhost:3000/api/system/status
```

响应示例：
```json
{
  "code": "SUCCESS",
  "message": "System status retrieved successfully",
  "results": {
    "total_instances": 3,
    "active_instances": 2,
    "connected_instances": 2,
    "instances": [
      {
        "id": "uuid-1",
        "name": "Account-1",
        "status": "connected",
        "last_active": "2024-01-01T12:00:00Z",
        "phone": "**********"
      }
    ],
    "system_info": {
      "version": "1.0.0-multiaccount",
      "start_time": "2024-01-01T10:00:00Z",
      "uptime": "2h30m15s"
    }
  }
}
```

## 🔒 安全特性

- ✅ 数据隔离：每个实例独立的数据库
- ✅ 网络隔离：独立的代理配置
- ✅ 认证保护：Basic Auth 支持
- ✅ 敏感信息保护：代理密码不在响应中返回

## 🛠️ 配置说明

### 实例配置
```json
{
  "name": "实例名称",
  "auto_reply": "自动回复消息",
  "auto_mark_read": false,
  "webhook": "webhook URL",
  "webhook_secret": "webhook 密钥",
  "account_validation": true,
  "chat_storage": true,
  "debug": false
}
```

### 代理配置
```json
{
  "type": "socks5",        // socks5 或 http
  "host": "proxy.com",     // 代理主机
  "port": 1080,            // 代理端口
  "username": "user",      // 用户名（可选）
  "password": "pass",      // 密码（可选）
  "enabled": true          // 是否启用
}
```

## 📝 更新日志

### v1.0.0-multiaccount
- ✅ 多账户支持
- ✅ 独立代理配置
- ✅ 实例管理 API
- ✅ 系统监控功能
- ✅ 数据隔离

## 🤝 贡献

基于原项目 [go-whatsapp-web-multidevice](https://github.com/aldinokemal/go-whatsapp-web-multidevice) 进行改造。

## 📄 许可证

继承原项目许可证。
