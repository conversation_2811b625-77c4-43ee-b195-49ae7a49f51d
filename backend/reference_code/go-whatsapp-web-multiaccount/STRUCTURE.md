# 项目结构说明

## 📁 目录结构

```
go-whatsapp-web-multiaccount/
├── README.md                    # 项目主要说明文档
├── PROJECT_SUMMARY.md           # 项目总结和功能说明
├── STRUCTURE.md                 # 项目结构说明 (本文件)
├── LICENCE.txt                  # 开源许可证
├── .gitignore                   # Git 忽略文件配置
├── start.sh                     # 项目启动脚本
├── test_multiaccount.sh         # 功能测试脚本
├── docker-compose.yml           # Docker 编排配置
├── bin/                         # 编译后的可执行文件
│   └── multiaccount            # 主程序可执行文件
├── docs/                        # 文档目录
│   ├── openapi.yaml            # API 文档 (OpenAPI 3.0)
│   └── webhook-payload.md      # Webhook 负载说明
├── docker/                      # Docker 相关文件
│   └── golang.Dockerfile      # Go 应用 Dockerfile
└── src/                        # 源代码目录
    ├── main.go                 # 程序入口
    ├── go.mod                  # Go 模块定义
    ├── go.sum                  # Go 依赖锁定文件
    ├── cmd/                    # 命令行相关
    │   ├── root.go            # 应用初始化
    │   └── rest.go            # REST 服务器
    ├── config/                 # 配置相关
    │   └── app.go             # 应用配置
    ├── domains/                # 领域模型
    │   ├── app/               # 应用领域
    │   ├── chat/              # 聊天领域
    │   ├── chatstorage/       # 聊天存储领域
    │   ├── group/             # 群组领域
    │   ├── instance/          # 实例领域 (新增)
    │   ├── message/           # 消息领域
    │   ├── newsletter/        # 频道领域
    │   ├── send/              # 发送领域
    │   └── user/              # 用户领域
    ├── infrastructure/        # 基础设施层
    │   ├── chatstorage/       # 聊天存储实现
    │   └── whatsapp/          # WhatsApp 客户端实现
    ├── pkg/                   # 公共包
    │   ├── error/             # 错误处理
    │   ├── manager/           # 实例管理器 (新增)
    │   └── utils/             # 工具函数
    ├── statics/               # 静态资源
    ├── storages/              # 数据存储目录 (运行时创建)
    ├── ui/                    # 用户界面层
    │   ├── rest/              # REST API 控制器
    │   └── websocket/         # WebSocket 处理
    ├── usecase/               # 用例层 (业务逻辑)
    ├── validations/           # 验证规则
    └── views/                 # 视图模板
```

## 🔧 核心组件说明

### 新增的多账户组件

#### 1. `pkg/manager/` - 实例管理器
- `instance_manager.go` - 多账户实例管理核心逻辑

#### 2. `domains/instance/` - 实例领域
- `entity.go` - 实例相关数据结构
- `interface.go` - 实例服务接口定义

#### 3. `usecase/instance_service.go` - 实例业务逻辑
- 实例创建、删除、管理
- 代理配置和测试
- 系统状态监控

#### 4. `ui/rest/instance_controller.go` - 实例 REST API
- 实例管理 API 端点
- 代理管理 API 端点
- 系统监控 API 端点

### 数据存储结构

```
src/storages/                   # 数据存储根目录
├── instance_{uuid}/            # 每个实例的独立目录
│   ├── whatsapp.db            # WhatsApp 会话数据
│   ├── whatsapp.db-wal        # SQLite WAL 文件
│   ├── whatsapp.db-shm        # SQLite 共享内存文件
│   ├── chat_storage.db        # 聊天记录数据
│   ├── chat_storage.db-wal    # SQLite WAL 文件
│   └── chat_storage.db-shm    # SQLite 共享内存文件
└── (其他实例目录...)
```

## 🚀 快速开始

### 1. 使用启动脚本 (推荐)
```bash
# 默认启动 (端口 3001, 调试模式)
./start.sh

# 自定义端口启动
./start.sh -p 8080

# 生产模式启动
./start.sh -d false

# 仅编译不启动
./start.sh --build-only
```

### 2. 手动编译和启动
```bash
cd src
go mod tidy
go build -o ../bin/multiaccount main.go
../bin/multiaccount rest --port=3001 --debug=true
```

### 3. 功能测试
```bash
# 运行完整功能测试
./test_multiaccount.sh
```

## 📋 API 端点

### 实例管理
- `POST /api/instances` - 创建实例
- `GET /api/instances` - 获取实例列表
- `GET /api/instances/{id}` - 获取实例详情
- `PUT /api/instances/{id}` - 更新实例配置
- `DELETE /api/instances/{id}` - 删除实例

### 实例操作
- `POST /api/instances/{id}/login` - 实例登录
- `POST /api/instances/{id}/logout` - 实例登出
- `GET /api/instances/{id}/status` - 实例状态
- `POST /api/instances/{id}/reconnect` - 重新连接

### 代理管理
- `POST /api/proxy/test` - 测试代理连接
- `PUT /api/instances/{id}/proxy` - 更新实例代理

### 系统管理
- `GET /api/system/status` - 系统状态

### 传统 API (向后兼容)
- 所有原有的单实例 API 端点继续可用

## 🔒 安全特性

- **数据隔离**: 每个实例独立的数据库和存储
- **网络隔离**: 独立的代理配置
- **认证保护**: Basic Auth 支持
- **敏感信息保护**: 代理密码等不在响应中返回

## 📝 开发说明

### 添加新功能
1. 在 `domains/` 中定义领域模型
2. 在 `usecase/` 中实现业务逻辑
3. 在 `ui/rest/` 中添加 API 端点
4. 在 `infrastructure/` 中实现基础设施

### 数据库迁移
- 每个实例的数据库会在创建时自动初始化
- 数据库结构变更需要在实例管理器中处理

### 测试
- 使用 `test_multiaccount.sh` 进行功能测试
- 单元测试可以在各个包中添加 `*_test.go` 文件
