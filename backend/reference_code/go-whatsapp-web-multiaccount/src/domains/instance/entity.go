package instance

import (
	"time"
	"github.com/jlu/go-whatsapp-web-multiaccount/pkg/manager"
)

// CreateInstanceRequest 创建实例请求
type CreateInstanceRequest struct {
	Name        string                   `json:"name" validate:"required"`
	Config      *manager.InstanceConfig  `json:"config"`
	ProxyConfig *manager.ProxyConfig     `json:"proxy_config"`
}

// UpdateInstanceRequest 更新实例请求
type UpdateInstanceRequest struct {
	Name        string                   `json:"name"`
	Config      *manager.InstanceConfig  `json:"config"`
	ProxyConfig *manager.ProxyConfig     `json:"proxy_config"`
}

// InstanceResponse 实例响应
type InstanceResponse struct {
	ID          string                   `json:"id"`
	Name        string                   `json:"name"`
	Status      manager.InstanceStatus   `json:"status"`
	Config      *manager.InstanceConfig  `json:"config"`
	ProxyConfig *ProxyConfigResponse     `json:"proxy_config"`
	CreatedAt   time.Time                `json:"created_at"`
	LastActive  time.Time                `json:"last_active"`
	Phone       string                   `json:"phone,omitempty"`
	PushName    string                   `json:"push_name,omitempty"`
}

// ProxyConfigResponse 代理配置响应（隐藏敏感信息）
type ProxyConfigResponse struct {
	Type    string `json:"type"`
	Host    string `json:"host"`
	Port    int    `json:"port"`
	Enabled bool   `json:"enabled"`
	// 不返回用户名和密码
}

// LoginResponse 登录响应
type LoginResponse struct {
	InstanceID string `json:"instance_id"`
	QRCode     string `json:"qr_code,omitempty"`
	Code       string `json:"code,omitempty"`
	Message    string `json:"message"`
}

// StatusResponse 状态响应
type StatusResponse struct {
	InstanceID string                 `json:"instance_id"`
	Status     manager.InstanceStatus `json:"status"`
	IsLoggedIn bool                   `json:"is_logged_in"`
	Phone      string                 `json:"phone,omitempty"`
	PushName   string                 `json:"push_name,omitempty"`
	Device     interface{}            `json:"device,omitempty"`
}

// ProxyTestRequest 代理测试请求
type ProxyTestRequest struct {
	Type     string `json:"type" validate:"required"`
	Host     string `json:"host" validate:"required"`
	Port     int    `json:"port" validate:"required"`
	Username string `json:"username"`
	Password string `json:"password"`
}

// ProxyTestResponse 代理测试响应
type ProxyTestResponse struct {
	Success     bool   `json:"success"`
	Message     string `json:"message"`
	Latency     int64  `json:"latency_ms,omitempty"`
	ExternalIP  string `json:"external_ip,omitempty"`
}

// SystemStatusResponse 系统状态响应
type SystemStatusResponse struct {
	TotalInstances   int                    `json:"total_instances"`
	ActiveInstances  int                    `json:"active_instances"`
	ConnectedInstances int                  `json:"connected_instances"`
	Instances        []InstanceSummary      `json:"instances"`
	SystemInfo       SystemInfo             `json:"system_info"`
}

// InstanceSummary 实例摘要
type InstanceSummary struct {
	ID         string                 `json:"id"`
	Name       string                 `json:"name"`
	Status     manager.InstanceStatus `json:"status"`
	LastActive time.Time              `json:"last_active"`
	Phone      string                 `json:"phone,omitempty"`
}

// SystemInfo 系统信息
type SystemInfo struct {
	Version   string    `json:"version"`
	StartTime time.Time `json:"start_time"`
	Uptime    string    `json:"uptime"`
}
