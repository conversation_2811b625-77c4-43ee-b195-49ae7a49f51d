package instance

import (
	"context"
	"github.com/jlu/go-whatsapp-web-multiaccount/pkg/manager"
)

// IInstanceUsecase 实例用例接口
type IInstanceUsecase interface {
	// 实例管理
	CreateInstance(ctx context.Context, req *CreateInstanceRequest) (*InstanceResponse, error)
	GetInstance(ctx context.Context, instanceID string) (*InstanceResponse, error)
	ListInstances(ctx context.Context) ([]InstanceResponse, error)
	UpdateInstance(ctx context.Context, instanceID string, req *UpdateInstanceRequest) (*InstanceResponse, error)
	DeleteInstance(ctx context.Context, instanceID string) error

	// 实例操作
	LoginInstance(ctx context.Context, instanceID string) (*LoginResponse, error)
	LoginInstanceWithCode(ctx context.Context, instanceID string, code string) (*LoginResponse, error)
	LogoutInstance(ctx context.Context, instanceID string) error
	GetInstanceStatus(ctx context.Context, instanceID string) (*StatusResponse, error)
	ReconnectInstance(ctx context.Context, instanceID string) error

	// 代理管理
	TestProxy(ctx context.Context, req *ProxyTestRequest) (*ProxyTestResponse, error)
	UpdateInstanceProxy(ctx context.Context, instanceID string, proxyConfig *manager.ProxyConfig) error
	SwitchInstanceProxy(ctx context.Context, instanceID string, proxyConfig *manager.ProxyConfig) error

	// 系统管理
	GetSystemStatus(ctx context.Context) (*SystemStatusResponse, error)
}

// IInstanceRepository 实例仓库接口
type IInstanceRepository interface {
	// 持久化实例配置
	SaveInstanceConfig(instanceID string, config *manager.InstanceConfig) error
	LoadInstanceConfig(instanceID string) (*manager.InstanceConfig, error)
	DeleteInstanceConfig(instanceID string) error
	
	// 实例元数据
	SaveInstanceMetadata(instanceID string, metadata map[string]interface{}) error
	LoadInstanceMetadata(instanceID string) (map[string]interface{}, error)
}
