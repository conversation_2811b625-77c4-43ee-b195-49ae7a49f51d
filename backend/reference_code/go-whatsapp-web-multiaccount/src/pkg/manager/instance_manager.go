package manager

import (
	"context"
	"database/sql"
	"fmt"
	"os"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"go.mau.fi/whatsmeow"
	"go.mau.fi/whatsmeow/store/sqlstore"
	waLog "go.mau.fi/whatsmeow/util/log"

	"github.com/jlu/go-whatsapp-web-multiaccount/config"
	domainChatStorage "github.com/jlu/go-whatsapp-web-multiaccount/domains/chatstorage"
	"github.com/jlu/go-whatsapp-web-multiaccount/infrastructure/chatstorage"
	"github.com/jlu/go-whatsapp-web-multiaccount/infrastructure/whatsapp"
)

// InstanceManager 管理多个 WhatsApp 实例
type InstanceManager struct {
	instances map[string]*WhatsAppInstance
	mutex     sync.RWMutex
	ctx       context.Context
}

// WhatsAppInstance 单个 WhatsApp 实例
type WhatsAppInstance struct {
	ID              string                                `json:"id"`
	Name            string                                `json:"name"`
	Client          *whatsmeow.Client                     `json:"-"`
	DB              *sqlstore.Container                   `json:"-"`
	ChatStorageDB   *sql.DB                               `json:"-"`
	ChatStorageRepo domainChatStorage.IChatStorageRepository `json:"-"`
	Config          *InstanceConfig                       `json:"config"`
	ProxyConfig     *ProxyConfig                          `json:"proxy_config"`
	Status          InstanceStatus                        `json:"status"`
	CreatedAt       time.Time                             `json:"created_at"`
	LastActive      time.Time                             `json:"last_active"`
	Phone           string                                `json:"phone,omitempty"`
	PushName        string                                `json:"push_name,omitempty"`
}

// InstanceConfig 实例配置
type InstanceConfig struct {
	Name                    string `json:"name"`
	AutoReply              string `json:"auto_reply"`
	AutoMarkRead           bool   `json:"auto_mark_read"`
	Webhook                string `json:"webhook"`
	WebhookSecret          string `json:"webhook_secret"`
	AccountValidation      bool   `json:"account_validation"`
	ChatStorage            bool   `json:"chat_storage"`
	Debug                  bool   `json:"debug"`
}

// ProxyConfig 代理配置
type ProxyConfig struct {
	Type     string `json:"type"`     // socks5, http
	Host     string `json:"host"`
	Port     int    `json:"port"`
	Username string `json:"username"`
	Password string `json:"password"`
	Enabled  bool   `json:"enabled"`
}

// InstanceStatus 实例状态
type InstanceStatus string

const (
	StatusCreated      InstanceStatus = "created"
	StatusConnecting   InstanceStatus = "connecting"
	StatusConnected    InstanceStatus = "connected"
	StatusDisconnected InstanceStatus = "disconnected"
	StatusError        InstanceStatus = "error"
	StatusLoggedOut    InstanceStatus = "logged_out"
)

// NewInstanceManager 创建实例管理器
func NewInstanceManager(ctx context.Context) *InstanceManager {
	return &InstanceManager{
		instances: make(map[string]*WhatsAppInstance),
		ctx:       ctx,
	}
}

// CreateInstance 创建新实例
func (im *InstanceManager) CreateInstance(name string, config *InstanceConfig, proxyConfig *ProxyConfig) (*WhatsAppInstance, error) {
	im.mutex.Lock()
	defer im.mutex.Unlock()

	instanceID := uuid.New().String()
	
	// 创建实例目录
	instanceDir := fmt.Sprintf("storages/instance_%s", instanceID)
	if err := os.MkdirAll(instanceDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create instance directory: %w", err)
	}

	// 创建独立的数据库
	dbPath := fmt.Sprintf("%s/whatsapp.db", instanceDir)
	dbURI := fmt.Sprintf("file:%s?_foreign_keys=on", dbPath)
	
	// 初始化 WhatsApp 数据库
	whatsappDB := whatsapp.InitWaDB(im.ctx, dbURI)
	
	// 创建聊天存储数据库
	chatStorageDB, err := im.initChatStorage(instanceID)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize chat storage: %w", err)
	}
	
	chatStorageRepo := chatstorage.NewStorageRepository(chatStorageDB)
	chatStorageRepo.InitializeSchema()

	// 创建带代理的客户端
	client, err := im.createClientWithProxy(whatsappDB, proxyConfig, instanceID)
	if err != nil {
		return nil, fmt.Errorf("failed to create client: %w", err)
	}

	instance := &WhatsAppInstance{
		ID:              instanceID,
		Name:            name,
		Client:          client,
		DB:              whatsappDB,
		ChatStorageDB:   chatStorageDB,
		ChatStorageRepo: chatStorageRepo,
		Config:          config,
		ProxyConfig:     proxyConfig,
		Status:          StatusCreated,
		CreatedAt:       time.Now(),
		LastActive:      time.Now(),
	}

	im.instances[instanceID] = instance
	
	logrus.Infof("Created instance %s (%s) with proxy: %v", instanceID, name, proxyConfig.Enabled)
	
	return instance, nil
}

// initChatStorage 初始化聊天存储
func (im *InstanceManager) initChatStorage(instanceID string) (*sql.DB, error) {
	dbPath := fmt.Sprintf("storages/instance_%s/chat_storage.db", instanceID)
	connStr := fmt.Sprintf("%s?_journal_mode=WAL&_foreign_keys=on", dbPath)

	db, err := sql.Open("sqlite3", connStr)
	if err != nil {
		return nil, err
	}

	// 配置连接池
	db.SetMaxOpenConns(25)
	db.SetMaxIdleConns(5)

	// 测试连接
	if err := db.Ping(); err != nil {
		db.Close()
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	return db, nil
}

// createClientWithProxy 创建带代理的客户端
func (im *InstanceManager) createClientWithProxy(db *sqlstore.Container, proxyConfig *ProxyConfig, instanceID string) (*whatsmeow.Client, error) {
	device, err := db.GetFirstDevice(im.ctx)
	if err != nil {
		return nil, err
	}

	if device == nil {
		return nil, fmt.Errorf("no device found for instance %s", instanceID)
	}

	// 创建日志记录器
	logger := waLog.Stdout(fmt.Sprintf("Instance-%s", instanceID[:8]), config.WhatsappLogLevel, true)

	// 创建客户端
	client := whatsmeow.NewClient(device, logger)
	client.EnableAutoReconnect = true
	client.AutoTrustIdentity = true

	// 配置代理
	if proxyConfig != nil && proxyConfig.Enabled {
		var proxyAddr string
		if proxyConfig.Username != "" && proxyConfig.Password != "" {
			proxyAddr = fmt.Sprintf("%s://%s:%s@%s:%d",
				proxyConfig.Type,
				proxyConfig.Username,
				proxyConfig.Password,
				proxyConfig.Host,
				proxyConfig.Port)
		} else {
			proxyAddr = fmt.Sprintf("%s://%s:%d",
				proxyConfig.Type,
				proxyConfig.Host,
				proxyConfig.Port)
		}

		// 使用 whatsmeow 的代理设置方法
		err = client.SetProxyAddress(proxyAddr)
		if err != nil {
			return nil, fmt.Errorf("failed to set proxy: %w", err)
		}

		logrus.Infof("Instance %s configured with proxy: %s://%s:%d", instanceID, proxyConfig.Type, proxyConfig.Host, proxyConfig.Port)
	}

	return client, nil
}

// GetInstance 获取实例
func (im *InstanceManager) GetInstance(instanceID string) (*WhatsAppInstance, bool) {
	im.mutex.RLock()
	defer im.mutex.RUnlock()

	instance, exists := im.instances[instanceID]
	if exists {
		instance.LastActive = time.Now()
	}

	return instance, exists
}

// ListInstances 获取所有实例
func (im *InstanceManager) ListInstances() []*WhatsAppInstance {
	im.mutex.RLock()
	defer im.mutex.RUnlock()

	instances := make([]*WhatsAppInstance, 0, len(im.instances))
	for _, instance := range im.instances {
		instances = append(instances, instance)
	}

	return instances
}

// DeleteInstance 删除实例
func (im *InstanceManager) DeleteInstance(instanceID string) error {
	im.mutex.Lock()
	defer im.mutex.Unlock()

	instance, exists := im.instances[instanceID]
	if !exists {
		return fmt.Errorf("instance %s not found", instanceID)
	}

	// 断开连接
	if instance.Client != nil {
		instance.Client.Disconnect()
	}

	// 关闭数据库连接
	if instance.ChatStorageDB != nil {
		instance.ChatStorageDB.Close()
	}
	
	if instance.DB != nil {
		instance.DB.Close()
	}

	// 删除实例目录
	instanceDir := fmt.Sprintf("storages/instance_%s", instanceID)
	if err := os.RemoveAll(instanceDir); err != nil {
		logrus.Warnf("Failed to remove instance directory %s: %v", instanceDir, err)
	}

	delete(im.instances, instanceID)
	
	logrus.Infof("Deleted instance %s (%s)", instanceID, instance.Name)
	
	return nil
}

// UpdateInstanceStatus 更新实例状态
func (im *InstanceManager) UpdateInstanceStatus(instanceID string, status InstanceStatus) {
	im.mutex.Lock()
	defer im.mutex.Unlock()

	if instance, exists := im.instances[instanceID]; exists {
		instance.Status = status
		instance.LastActive = time.Now()
	}
}

// GetInstanceCount 获取实例数量
func (im *InstanceManager) GetInstanceCount() int {
	im.mutex.RLock()
	defer im.mutex.RUnlock()
	
	return len(im.instances)
}
