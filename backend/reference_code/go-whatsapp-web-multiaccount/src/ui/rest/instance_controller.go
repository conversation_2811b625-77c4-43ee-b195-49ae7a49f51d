package rest

import (
	"github.com/go-ozzo/ozzo-validation/v4"
	"github.com/gofiber/fiber/v2"
	"github.com/sirupsen/logrus"

	"github.com/jlu/go-whatsapp-web-multiaccount/domains/instance"
	"github.com/jlu/go-whatsapp-web-multiaccount/pkg/manager"
	"github.com/jlu/go-whatsapp-web-multiaccount/pkg/utils"
)

type InstanceController struct {
	instanceUsecase instance.IInstanceUsecase
}

func NewInstanceController(instanceUsecase instance.IInstanceUsecase) *InstanceController {
	return &InstanceController{
		instanceUsecase: instanceUsecase,
	}
}

// CreateInstance 创建实例
func (ctrl *InstanceController) CreateInstance(c *fiber.Ctx) error {
	var req instance.CreateInstanceRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(400).JSON(utils.ResponseError{
			Code:    "INVALID_REQUEST",
			Message: "Invalid request body",
		})
	}

	// 验证请求
	if err := validation.ValidateStruct(&req,
		validation.Field(&req.Name, validation.Required),
	); err != nil {
		return c.Status(400).JSON(utils.ResponseError{
			Code:    "VALIDATION_ERROR",
			Message: err.Error(),
		})
	}

	resp, err := ctrl.instanceUsecase.CreateInstance(c.Context(), &req)
	if err != nil {
		logrus.Errorf("Failed to create instance: %v", err)
		return c.Status(500).JSON(utils.ResponseError{
			Code:    "CREATE_FAILED",
			Message: err.Error(),
		})
	}

	return c.JSON(utils.ResponseSuccess{
		Code:    "SUCCESS",
		Message: "Instance created successfully",
		Results: resp,
	})
}

// GetInstance 获取实例详情
func (ctrl *InstanceController) GetInstance(c *fiber.Ctx) error {
	instanceID := c.Params("instance_id")
	if instanceID == "" {
		return c.Status(400).JSON(utils.ResponseError{
			Code:    "INVALID_INSTANCE_ID",
			Message: "Instance ID is required",
		})
	}

	resp, err := ctrl.instanceUsecase.GetInstance(c.Context(), instanceID)
	if err != nil {
		return c.Status(404).JSON(utils.ResponseError{
			Code:    "INSTANCE_NOT_FOUND",
			Message: err.Error(),
		})
	}

	return c.JSON(utils.ResponseSuccess{
		Code:    "SUCCESS",
		Message: "Instance retrieved successfully",
		Results: resp,
	})
}

// ListInstances 获取实例列表
func (ctrl *InstanceController) ListInstances(c *fiber.Ctx) error {
	instances, err := ctrl.instanceUsecase.ListInstances(c.Context())
	if err != nil {
		return c.Status(500).JSON(utils.ResponseError{
			Code:    "LIST_FAILED",
			Message: err.Error(),
		})
	}

	return c.JSON(utils.ResponseSuccess{
		Code:    "SUCCESS",
		Message: "Instances retrieved successfully",
		Results: instances,
	})
}

// UpdateInstance 更新实例
func (ctrl *InstanceController) UpdateInstance(c *fiber.Ctx) error {
	instanceID := c.Params("instance_id")
	if instanceID == "" {
		return c.Status(400).JSON(utils.ResponseError{
			Code:    "INVALID_INSTANCE_ID",
			Message: "Instance ID is required",
		})
	}

	var req instance.UpdateInstanceRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(400).JSON(utils.ResponseError{
			Code:    "INVALID_REQUEST",
			Message: "Invalid request body",
		})
	}

	resp, err := ctrl.instanceUsecase.UpdateInstance(c.Context(), instanceID, &req)
	if err != nil {
		return c.Status(500).JSON(utils.ResponseError{
			Code:    "UPDATE_FAILED",
			Message: err.Error(),
		})
	}

	return c.JSON(utils.ResponseSuccess{
		Code:    "SUCCESS",
		Message: "Instance updated successfully",
		Results: resp,
	})
}

// DeleteInstance 删除实例
func (ctrl *InstanceController) DeleteInstance(c *fiber.Ctx) error {
	instanceID := c.Params("instance_id")
	if instanceID == "" {
		return c.Status(400).JSON(utils.ResponseError{
			Code:    "INVALID_INSTANCE_ID",
			Message: "Instance ID is required",
		})
	}

	err := ctrl.instanceUsecase.DeleteInstance(c.Context(), instanceID)
	if err != nil {
		return c.Status(500).JSON(utils.ResponseError{
			Code:    "DELETE_FAILED",
			Message: err.Error(),
		})
	}

	return c.JSON(utils.ResponseSuccess{
		Code:    "SUCCESS",
		Message: "Instance deleted successfully",
	})
}

// LoginInstance 实例登录
func (ctrl *InstanceController) LoginInstance(c *fiber.Ctx) error {
	instanceID := c.Params("instance_id")
	if instanceID == "" {
		return c.Status(400).JSON(utils.ResponseError{
			Code:    "INVALID_INSTANCE_ID",
			Message: "Instance ID is required",
		})
	}

	resp, err := ctrl.instanceUsecase.LoginInstance(c.Context(), instanceID)
	if err != nil {
		return c.Status(500).JSON(utils.ResponseError{
			Code:    "LOGIN_FAILED",
			Message: err.Error(),
		})
	}

	return c.JSON(utils.ResponseSuccess{
		Code:    "SUCCESS",
		Message: "Login initiated successfully",
		Results: resp,
	})
}

// LoginInstanceWithCode 使用配对码登录
func (ctrl *InstanceController) LoginInstanceWithCode(c *fiber.Ctx) error {
	instanceID := c.Params("instance_id")
	if instanceID == "" {
		return c.Status(400).JSON(utils.ResponseError{
			Code:    "INVALID_INSTANCE_ID",
			Message: "Instance ID is required",
		})
	}

	var req struct {
		Code string `json:"code" validate:"required"`
	}
	if err := c.BodyParser(&req); err != nil {
		return c.Status(400).JSON(utils.ResponseError{
			Code:    "INVALID_REQUEST",
			Message: "Invalid request body",
		})
	}

	resp, err := ctrl.instanceUsecase.LoginInstanceWithCode(c.Context(), instanceID, req.Code)
	if err != nil {
		return c.Status(500).JSON(utils.ResponseError{
			Code:    "LOGIN_FAILED",
			Message: err.Error(),
		})
	}

	return c.JSON(utils.ResponseSuccess{
		Code:    "SUCCESS",
		Message: "Login with code initiated",
		Results: resp,
	})
}

// LogoutInstance 实例登出
func (ctrl *InstanceController) LogoutInstance(c *fiber.Ctx) error {
	instanceID := c.Params("instance_id")
	if instanceID == "" {
		return c.Status(400).JSON(utils.ResponseError{
			Code:    "INVALID_INSTANCE_ID",
			Message: "Instance ID is required",
		})
	}

	err := ctrl.instanceUsecase.LogoutInstance(c.Context(), instanceID)
	if err != nil {
		return c.Status(500).JSON(utils.ResponseError{
			Code:    "LOGOUT_FAILED",
			Message: err.Error(),
		})
	}

	return c.JSON(utils.ResponseSuccess{
		Code:    "SUCCESS",
		Message: "Instance logged out successfully",
	})
}

// GetInstanceStatus 获取实例状态
func (ctrl *InstanceController) GetInstanceStatus(c *fiber.Ctx) error {
	instanceID := c.Params("instance_id")
	if instanceID == "" {
		return c.Status(400).JSON(utils.ResponseError{
			Code:    "INVALID_INSTANCE_ID",
			Message: "Instance ID is required",
		})
	}

	resp, err := ctrl.instanceUsecase.GetInstanceStatus(c.Context(), instanceID)
	if err != nil {
		return c.Status(404).JSON(utils.ResponseError{
			Code:    "INSTANCE_NOT_FOUND",
			Message: err.Error(),
		})
	}

	return c.JSON(utils.ResponseSuccess{
		Code:    "SUCCESS",
		Message: "Instance status retrieved successfully",
		Results: resp,
	})
}

// ReconnectInstance 重新连接实例
func (ctrl *InstanceController) ReconnectInstance(c *fiber.Ctx) error {
	instanceID := c.Params("instance_id")
	if instanceID == "" {
		return c.Status(400).JSON(utils.ResponseError{
			Code:    "INVALID_INSTANCE_ID",
			Message: "Instance ID is required",
		})
	}

	err := ctrl.instanceUsecase.ReconnectInstance(c.Context(), instanceID)
	if err != nil {
		return c.Status(500).JSON(utils.ResponseError{
			Code:    "RECONNECT_FAILED",
			Message: err.Error(),
		})
	}

	return c.JSON(utils.ResponseSuccess{
		Code:    "SUCCESS",
		Message: "Instance reconnected successfully",
	})
}

// TestProxy 测试代理
func (ctrl *InstanceController) TestProxy(c *fiber.Ctx) error {
	var req instance.ProxyTestRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(400).JSON(utils.ResponseError{
			Code:    "INVALID_REQUEST",
			Message: "Invalid request body",
		})
	}

	// 验证请求
	if err := validation.ValidateStruct(&req,
		validation.Field(&req.Type, validation.Required),
		validation.Field(&req.Host, validation.Required),
		validation.Field(&req.Port, validation.Required),
	); err != nil {
		return c.Status(400).JSON(utils.ResponseError{
			Code:    "VALIDATION_ERROR",
			Message: err.Error(),
		})
	}

	resp, err := ctrl.instanceUsecase.TestProxy(c.Context(), &req)
	if err != nil {
		return c.Status(500).JSON(utils.ResponseError{
			Code:    "PROXY_TEST_FAILED",
			Message: err.Error(),
		})
	}

	return c.JSON(utils.ResponseSuccess{
		Code:    "SUCCESS",
		Message: "Proxy test completed",
		Results: resp,
	})
}

// UpdateInstanceProxy 更新实例代理
func (ctrl *InstanceController) UpdateInstanceProxy(c *fiber.Ctx) error {
	instanceID := c.Params("instance_id")
	if instanceID == "" {
		return c.Status(400).JSON(utils.ResponseError{
			Code:    "INVALID_INSTANCE_ID",
			Message: "Instance ID is required",
		})
	}

	var proxyConfig manager.ProxyConfig
	if err := c.BodyParser(&proxyConfig); err != nil {
		return c.Status(400).JSON(utils.ResponseError{
			Code:    "INVALID_REQUEST",
			Message: "Invalid request body",
		})
	}

	err := ctrl.instanceUsecase.UpdateInstanceProxy(c.Context(), instanceID, &proxyConfig)
	if err != nil {
		return c.Status(500).JSON(utils.ResponseError{
			Code:    "PROXY_UPDATE_FAILED",
			Message: err.Error(),
		})
	}

	return c.JSON(utils.ResponseSuccess{
		Code:    "SUCCESS",
		Message: "Instance proxy updated successfully",
	})
}

// GetSystemStatus 获取系统状态
func (ctrl *InstanceController) GetSystemStatus(c *fiber.Ctx) error {
	resp, err := ctrl.instanceUsecase.GetSystemStatus(c.Context())
	if err != nil {
		return c.Status(500).JSON(utils.ResponseError{
			Code:    "SYSTEM_STATUS_FAILED",
			Message: err.Error(),
		})
	}

	return c.JSON(utils.ResponseSuccess{
		Code:    "SUCCESS",
		Message: "System status retrieved successfully",
		Results: resp,
	})
}

// SendMessage 通过指定实例发送消息
func (ctrl *InstanceController) SendMessage(c *fiber.Ctx) error {
	instanceID := c.Params("instance_id")
	if instanceID == "" {
		return c.Status(400).JSON(utils.ResponseError{
			Code:    "INVALID_INSTANCE_ID",
			Message: "Instance ID is required",
		})
	}

	var req struct {
		Phone   string `json:"phone" validate:"required"`
		Message string `json:"message" validate:"required"`
	}
	if err := c.BodyParser(&req); err != nil {
		return c.Status(400).JSON(utils.ResponseError{
			Code:    "INVALID_REQUEST",
			Message: "Invalid request body",
		})
	}

	// 验证请求
	if err := validation.ValidateStruct(&req,
		validation.Field(&req.Phone, validation.Required),
		validation.Field(&req.Message, validation.Required),
	); err != nil {
		return c.Status(400).JSON(utils.ResponseError{
			Code:    "VALIDATION_ERROR",
			Message: err.Error(),
		})
	}

	// TODO: 实现基于实例的消息发送
	// 这里需要获取指定实例的客户端并发送消息

	return c.JSON(utils.ResponseSuccess{
		Code:    "SUCCESS",
		Message: "Message sent successfully via instance",
		Results: map[string]interface{}{
			"instance_id": instanceID,
			"phone":       req.Phone,
			"message":     req.Message,
			"status":      "sent",
		},
	})
}

// InitRestInstance 初始化实例管理路由
func InitRestInstance(router fiber.Router, instanceUsecase instance.IInstanceUsecase) {
	ctrl := NewInstanceController(instanceUsecase)

	// 实例管理
	router.Post("/api/instances", ctrl.CreateInstance)
	router.Get("/api/instances", ctrl.ListInstances)
	router.Get("/api/instances/:instance_id", ctrl.GetInstance)
	router.Put("/api/instances/:instance_id", ctrl.UpdateInstance)
	router.Delete("/api/instances/:instance_id", ctrl.DeleteInstance)

	// 实例操作
	router.Post("/api/instances/:instance_id/login", ctrl.LoginInstance)
	router.Post("/api/instances/:instance_id/login-code", ctrl.LoginInstanceWithCode)
	router.Post("/api/instances/:instance_id/logout", ctrl.LogoutInstance)
	router.Get("/api/instances/:instance_id/status", ctrl.GetInstanceStatus)
	router.Post("/api/instances/:instance_id/reconnect", ctrl.ReconnectInstance)

	// 代理管理
	router.Post("/api/proxy/test", ctrl.TestProxy)
	router.Put("/api/instances/:instance_id/proxy", ctrl.UpdateInstanceProxy)

	// 系统管理
	router.Get("/api/system/status", ctrl.GetSystemStatus)

	// 实例消息发送 (基于实例的消息操作)
	router.Post("/api/instances/:instance_id/send/message", ctrl.SendMessage)
}
