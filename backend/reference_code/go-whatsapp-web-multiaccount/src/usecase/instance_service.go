package usecase

import (
	"context"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"

	"github.com/jlu/go-whatsapp-web-multiaccount/domains/instance"
	"github.com/jlu/go-whatsapp-web-multiaccount/pkg/manager"
	"github.com/jlu/go-whatsapp-web-multiaccount/pkg/utils"
)

type instanceService struct {
	instanceManager *manager.InstanceManager
	startTime       time.Time
}

// NewInstanceService 创建实例服务
func NewInstanceService(instanceManager *manager.InstanceManager) instance.IInstanceUsecase {
	return &instanceService{
		instanceManager: instanceManager,
		startTime:       time.Now(),
	}
}

// CreateInstance 创建实例
func (s *instanceService) CreateInstance(ctx context.Context, req *instance.CreateInstanceRequest) (*instance.InstanceResponse, error) {
	// 设置默认配置
	if req.Config == nil {
		req.Config = &manager.InstanceConfig{
			Name:              req.Name,
			AutoReply:         "",
			AutoMarkRead:      false,
			Webhook:           "",
			WebhookSecret:     "",
			AccountValidation: true,
			ChatStorage:       true,
			Debug:             false,
		}
	}

	// 设置默认代理配置
	if req.ProxyConfig == nil {
		req.ProxyConfig = &manager.ProxyConfig{
			Enabled: false,
		}
	}

	// 创建实例
	inst, err := s.instanceManager.CreateInstance(req.Name, req.Config, req.ProxyConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create instance: %w", err)
	}

	return s.convertToResponse(inst), nil
}

// GetInstance 获取实例
func (s *instanceService) GetInstance(ctx context.Context, instanceID string) (*instance.InstanceResponse, error) {
	inst, exists := s.instanceManager.GetInstance(instanceID)
	if !exists {
		return nil, fmt.Errorf("instance %s not found", instanceID)
	}

	return s.convertToResponse(inst), nil
}

// ListInstances 获取实例列表
func (s *instanceService) ListInstances(ctx context.Context) ([]instance.InstanceResponse, error) {
	instances := s.instanceManager.ListInstances()
	
	responses := make([]instance.InstanceResponse, len(instances))
	for i, inst := range instances {
		responses[i] = *s.convertToResponse(inst)
	}

	return responses, nil
}

// UpdateInstance 更新实例
func (s *instanceService) UpdateInstance(ctx context.Context, instanceID string, req *instance.UpdateInstanceRequest) (*instance.InstanceResponse, error) {
	inst, exists := s.instanceManager.GetInstance(instanceID)
	if !exists {
		return nil, fmt.Errorf("instance %s not found", instanceID)
	}

	// 更新配置
	if req.Name != "" {
		inst.Name = req.Name
	}
	if req.Config != nil {
		inst.Config = req.Config
	}
	if req.ProxyConfig != nil {
		inst.ProxyConfig = req.ProxyConfig
		// TODO: 重新配置客户端代理
	}

	return s.convertToResponse(inst), nil
}

// DeleteInstance 删除实例
func (s *instanceService) DeleteInstance(ctx context.Context, instanceID string) error {
	return s.instanceManager.DeleteInstance(instanceID)
}

// LoginInstance 实例登录
func (s *instanceService) LoginInstance(ctx context.Context, instanceID string) (*instance.LoginResponse, error) {
	inst, exists := s.instanceManager.GetInstance(instanceID)
	if !exists {
		return nil, fmt.Errorf("instance %s not found", instanceID)
	}

	if inst.Client == nil {
		return nil, fmt.Errorf("client not initialized for instance %s", instanceID)
	}

	// 检查是否已经登录
	if inst.Client.IsLoggedIn() {
		return &instance.LoginResponse{
			InstanceID: instanceID,
			Message:    "Already logged in",
		}, nil
	}

	// 生成二维码
	qrCode, err := inst.Client.GetQRChannel(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get QR channel: %w", err)
	}

	err = inst.Client.Connect()
	if err != nil {
		return nil, fmt.Errorf("failed to connect: %w", err)
	}

	// 等待二维码
	select {
	case evt := <-qrCode:
		if evt.Event == "code" {
			s.instanceManager.UpdateInstanceStatus(instanceID, manager.StatusConnecting)
			
			// 生成二维码图片
			qrCodeImage, err := utils.GenerateQRCode(evt.Code)
			if err != nil {
				return nil, fmt.Errorf("failed to generate QR code: %w", err)
			}

			return &instance.LoginResponse{
				InstanceID: instanceID,
				QRCode:     qrCodeImage,
				Message:    "Scan QR code to login",
			}, nil
		}
	case <-time.After(30 * time.Second):
		return nil, fmt.Errorf("timeout waiting for QR code")
	}

	return nil, fmt.Errorf("failed to get QR code")
}

// LoginInstanceWithCode 使用配对码登录
func (s *instanceService) LoginInstanceWithCode(ctx context.Context, instanceID string, code string) (*instance.LoginResponse, error) {
	inst, exists := s.instanceManager.GetInstance(instanceID)
	if !exists {
		return nil, fmt.Errorf("instance %s not found", instanceID)
	}

	if inst.Client == nil {
		return nil, fmt.Errorf("client not initialized for instance %s", instanceID)
	}

	// TODO: 实现配对码登录逻辑
	return &instance.LoginResponse{
		InstanceID: instanceID,
		Code:       code,
		Message:    "Pairing code login not implemented yet",
	}, fmt.Errorf("pairing code login not implemented")
}

// LogoutInstance 实例登出
func (s *instanceService) LogoutInstance(ctx context.Context, instanceID string) error {
	inst, exists := s.instanceManager.GetInstance(instanceID)
	if !exists {
		return fmt.Errorf("instance %s not found", instanceID)
	}

	if inst.Client != nil {
		inst.Client.Logout(ctx)
		inst.Client.Disconnect()
	}

	s.instanceManager.UpdateInstanceStatus(instanceID, manager.StatusLoggedOut)
	
	return nil
}

// GetInstanceStatus 获取实例状态
func (s *instanceService) GetInstanceStatus(ctx context.Context, instanceID string) (*instance.StatusResponse, error) {
	inst, exists := s.instanceManager.GetInstance(instanceID)
	if !exists {
		return nil, fmt.Errorf("instance %s not found", instanceID)
	}

	isLoggedIn := false
	var device interface{}
	
	if inst.Client != nil {
		isLoggedIn = inst.Client.IsLoggedIn()
		if isLoggedIn {
			device = inst.Client.Store.ID
		}
	}

	return &instance.StatusResponse{
		InstanceID: instanceID,
		Status:     inst.Status,
		IsLoggedIn: isLoggedIn,
		Phone:      inst.Phone,
		PushName:   inst.PushName,
		Device:     device,
	}, nil
}

// ReconnectInstance 重新连接实例
func (s *instanceService) ReconnectInstance(ctx context.Context, instanceID string) error {
	inst, exists := s.instanceManager.GetInstance(instanceID)
	if !exists {
		return fmt.Errorf("instance %s not found", instanceID)
	}

	if inst.Client != nil {
		inst.Client.Disconnect()
		time.Sleep(2 * time.Second)
		return inst.Client.Connect()
	}

	return fmt.Errorf("client not initialized for instance %s", instanceID)
}

// TestProxy 测试代理
func (s *instanceService) TestProxy(ctx context.Context, req *instance.ProxyTestRequest) (*instance.ProxyTestResponse, error) {
	start := time.Now()

	// TODO: 实现代理测试逻辑
	// 这里可以创建带代理的 HTTP 客户端并测试连接

	latency := time.Since(start).Milliseconds()

	return &instance.ProxyTestResponse{
		Success: true,
		Message: "Proxy test successful",
		Latency: latency,
	}, nil
}

// UpdateInstanceProxy 更新实例代理
func (s *instanceService) UpdateInstanceProxy(ctx context.Context, instanceID string, proxyConfig *manager.ProxyConfig) error {
	inst, exists := s.instanceManager.GetInstance(instanceID)
	if !exists {
		return fmt.Errorf("instance %s not found", instanceID)
	}

	inst.ProxyConfig = proxyConfig
	
	// TODO: 重新配置客户端代理
	logrus.Infof("Updated proxy config for instance %s", instanceID)
	
	return nil
}

// SwitchInstanceProxy 切换实例代理
func (s *instanceService) SwitchInstanceProxy(ctx context.Context, instanceID string, proxyConfig *manager.ProxyConfig) error {
	return s.UpdateInstanceProxy(ctx, instanceID, proxyConfig)
}

// GetSystemStatus 获取系统状态
func (s *instanceService) GetSystemStatus(ctx context.Context) (*instance.SystemStatusResponse, error) {
	instances := s.instanceManager.ListInstances()
	
	totalInstances := len(instances)
	activeInstances := 0
	connectedInstances := 0
	
	summaries := make([]instance.InstanceSummary, len(instances))
	
	for i, inst := range instances {
		if inst.Status != manager.StatusError {
			activeInstances++
		}
		if inst.Status == manager.StatusConnected {
			connectedInstances++
		}
		
		summaries[i] = instance.InstanceSummary{
			ID:         inst.ID,
			Name:       inst.Name,
			Status:     inst.Status,
			LastActive: inst.LastActive,
			Phone:      inst.Phone,
		}
	}

	uptime := time.Since(s.startTime).String()

	return &instance.SystemStatusResponse{
		TotalInstances:     totalInstances,
		ActiveInstances:    activeInstances,
		ConnectedInstances: connectedInstances,
		Instances:          summaries,
		SystemInfo: instance.SystemInfo{
			Version:   "1.0.0-multiaccount",
			StartTime: s.startTime,
			Uptime:    uptime,
		},
	}, nil
}

// convertToResponse 转换为响应格式
func (s *instanceService) convertToResponse(inst *manager.WhatsAppInstance) *instance.InstanceResponse {
	var proxyResp *instance.ProxyConfigResponse
	if inst.ProxyConfig != nil {
		proxyResp = &instance.ProxyConfigResponse{
			Type:    inst.ProxyConfig.Type,
			Host:    inst.ProxyConfig.Host,
			Port:    inst.ProxyConfig.Port,
			Enabled: inst.ProxyConfig.Enabled,
		}
	}

	return &instance.InstanceResponse{
		ID:          inst.ID,
		Name:        inst.Name,
		Status:      inst.Status,
		Config:      inst.Config,
		ProxyConfig: proxyResp,
		CreatedAt:   inst.CreatedAt,
		LastActive:  inst.LastActive,
		Phone:       inst.Phone,
		PushName:    inst.PushName,
	}
}
