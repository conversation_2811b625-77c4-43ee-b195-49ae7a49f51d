package usecase

import (
	"context"

	domainNewsletter "github.com/jlu/go-whatsapp-web-multiaccount/domains/newsletter"
	"github.com/jlu/go-whatsapp-web-multiaccount/infrastructure/whatsapp"
	"github.com/jlu/go-whatsapp-web-multiaccount/pkg/utils"
	"github.com/jlu/go-whatsapp-web-multiaccount/validations"
)

type serviceNewsletter struct{}

func NewNewsletterService() domainNewsletter.INewsletterUsecase {
	return &serviceNewsletter{}
}

func (service serviceNewsletter) Unfollow(ctx context.Context, request domainNewsletter.UnfollowRequest) (err error) {
	if err = validations.ValidateUnfollowNewsletter(ctx, request); err != nil {
		return err
	}

	JID, err := utils.ValidateJidWithLogin(whatsapp.GetClient(), request.NewsletterID)
	if err != nil {
		return err
	}

	return whatsapp.GetClient().UnfollowNewsletter(JID)
}
