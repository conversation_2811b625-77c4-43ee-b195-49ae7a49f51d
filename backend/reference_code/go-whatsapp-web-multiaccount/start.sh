#!/bin/bash

# GO WhatsApp Web Multi Account 启动脚本

set -e

PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SRC_DIR="$PROJECT_DIR/src"
BIN_DIR="$PROJECT_DIR/bin"
BINARY_NAME="multiaccount"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查 Go 环境
check_go() {
    if ! command -v go &> /dev/null; then
        print_message $RED "❌ Go 未安装，请先安装 Go 1.19 或更高版本"
        exit 1
    fi
    
    local go_version=$(go version | grep -oE 'go[0-9]+\.[0-9]+' | sed 's/go//')
    print_message $GREEN "✅ Go 版本: $go_version"
}

# 创建必要的目录
create_directories() {
    print_message $BLUE "📁 创建必要的目录..."
    mkdir -p "$BIN_DIR"
    mkdir -p "$SRC_DIR/storages"
    mkdir -p "$SRC_DIR/qrcode"
    mkdir -p "$SRC_DIR/send_items"
    mkdir -p "$SRC_DIR/media"
}

# 编译项目
build_project() {
    print_message $BLUE "🔨 编译项目..."
    cd "$SRC_DIR"
    
    # 下载依赖
    print_message $YELLOW "📦 下载依赖..."
    go mod tidy
    
    # 编译
    print_message $YELLOW "⚙️ 编译中..."
    go build -o "$BIN_DIR/$BINARY_NAME" main.go
    
    if [ $? -eq 0 ]; then
        print_message $GREEN "✅ 编译成功！"
    else
        print_message $RED "❌ 编译失败！"
        exit 1
    fi
}

# 启动服务
start_service() {
    local port=${1:-3001}
    local debug=${2:-true}
    
    print_message $BLUE "🚀 启动 GO WhatsApp Multi Account 服务..."
    print_message $YELLOW "端口: $port"
    print_message $YELLOW "调试模式: $debug"
    print_message $YELLOW "管理界面: http://localhost:$port"
    print_message $YELLOW "API 文档: http://localhost:$port/docs"
    echo ""
    
    cd "$SRC_DIR"
    "$BIN_DIR/$BINARY_NAME" rest --port="$port" --debug="$debug"
}

# 显示帮助信息
show_help() {
    echo "GO WhatsApp Web Multi Account 启动脚本"
    echo ""
    echo "用法:"
    echo "  $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -p, --port PORT     指定端口 (默认: 3001)"
    echo "  -d, --debug BOOL    调试模式 (默认: true)"
    echo "  -b, --build-only    仅编译，不启动"
    echo "  -h, --help          显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                  # 使用默认设置启动"
    echo "  $0 -p 8080          # 在端口 8080 启动"
    echo "  $0 -d false         # 关闭调试模式启动"
    echo "  $0 --build-only     # 仅编译项目"
}

# 主函数
main() {
    local port=3001
    local debug=true
    local build_only=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -p|--port)
                port="$2"
                shift 2
                ;;
            -d|--debug)
                debug="$2"
                shift 2
                ;;
            -b|--build-only)
                build_only=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                print_message $RED "❌ 未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    print_message $GREEN "🎯 GO WhatsApp Web Multi Account"
    print_message $GREEN "=================================="
    
    check_go
    create_directories
    build_project
    
    if [ "$build_only" = true ]; then
        print_message $GREEN "✅ 编译完成！可执行文件位于: $BIN_DIR/$BINARY_NAME"
        exit 0
    fi
    
    start_service "$port" "$debug"
}

# 运行主函数
main "$@"
