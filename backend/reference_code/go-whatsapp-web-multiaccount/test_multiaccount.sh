#!/bin/bash

# GO WhatsApp Web Multi Account 测试脚本
# 演示多账户管理功能

API_BASE="http://localhost:3001"
INSTANCE_ID=""

echo "🚀 GO WhatsApp Web Multi Account 测试脚本"
echo "=========================================="

# 1. 获取系统状态
echo "📊 1. 获取系统状态"
curl -s "$API_BASE/api/system/status" | jq '.'
echo ""

# 2. 创建第一个实例
echo "➕ 2. 创建第一个实例"
RESPONSE=$(curl -s -X POST "$API_BASE/api/instances" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Account-1",
    "config": {
      "auto_reply": "感谢您的消息，这是账户1的自动回复",
      "auto_mark_read": false,
      "debug": true
    },
    "proxy_config": {
      "type": "socks5",
      "host": "127.0.0.1",
      "port": 7890,
      "enabled": false
    }
  }')

echo "$RESPONSE" | jq '.'
INSTANCE_ID_1=$(echo "$RESPONSE" | jq -r '.results.id')
echo "实例1 ID: $INSTANCE_ID_1"
echo ""

# 3. 创建第二个实例（使用不同的代理）
echo "➕ 3. 创建第二个实例"
RESPONSE=$(curl -s -X POST "$API_BASE/api/instances" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Account-2",
    "config": {
      "auto_reply": "感谢您的消息，这是账户2的自动回复",
      "auto_mark_read": true,
      "debug": true
    },
    "proxy_config": {
      "type": "socks5",
      "host": "proxy2.example.com",
      "port": 1080,
      "username": "user2",
      "password": "pass2",
      "enabled": false
    }
  }')

echo "$RESPONSE" | jq '.'
INSTANCE_ID_2=$(echo "$RESPONSE" | jq -r '.results.id')
echo "实例2 ID: $INSTANCE_ID_2"
echo ""

# 4. 获取所有实例列表
echo "📋 4. 获取所有实例列表"
curl -s "$API_BASE/api/instances" | jq '.'
echo ""

# 5. 获取实例详情
echo "🔍 5. 获取实例1详情"
curl -s "$API_BASE/api/instances/$INSTANCE_ID_1" | jq '.'
echo ""

# 6. 获取实例状态
echo "📡 6. 获取实例1状态"
curl -s "$API_BASE/api/instances/$INSTANCE_ID_1/status" | jq '.'
echo ""

# 7. 测试代理
echo "🌐 7. 测试代理连接"
curl -s -X POST "$API_BASE/api/proxy/test" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "socks5",
    "host": "127.0.0.1",
    "port": 7890
  }' | jq '.'
echo ""

# 8. 更新实例配置
echo "⚙️ 8. 更新实例1配置"
curl -s -X PUT "$API_BASE/api/instances/$INSTANCE_ID_1" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Account-1-Updated",
    "config": {
      "auto_reply": "更新后的自动回复消息",
      "auto_mark_read": true,
      "debug": false
    }
  }' | jq '.'
echo ""

# 9. 再次获取系统状态
echo "📊 9. 更新后的系统状态"
curl -s "$API_BASE/api/system/status" | jq '.'
echo ""

# 10. 清理 - 删除测试实例
echo "🗑️ 10. 清理测试实例"
echo "删除实例1..."
curl -s -X DELETE "$API_BASE/api/instances/$INSTANCE_ID_1" | jq '.'

echo "删除实例2..."
curl -s -X DELETE "$API_BASE/api/instances/$INSTANCE_ID_2" | jq '.'
echo ""

# 11. 最终系统状态
echo "📊 11. 清理后的系统状态"
curl -s "$API_BASE/api/system/status" | jq '.'

echo ""
echo "✅ 测试完成！"
echo ""
echo "📝 测试总结："
echo "- ✅ 系统状态查询"
echo "- ✅ 创建多个实例"
echo "- ✅ 实例列表查询"
echo "- ✅ 实例详情查询"
echo "- ✅ 实例状态查询"
echo "- ✅ 代理测试"
echo "- ✅ 实例配置更新"
echo "- ✅ 实例删除"
echo ""
echo "🎉 GO WhatsApp Web Multi Account 功能测试成功！"
